package websocket_message

import (
	"context"
	"encoding/json"
	"fmt"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"strconv"
	"sync"
	"time"

	amqp "github.com/rabbitmq/amqp091-go"
)

// SendFunc is called for each consumed message.
type SendFunc func(uid int64, data map[string]interface{}) error

// WsMqWorker manages RabbitMQ connection, publishing, and subscribing.
type WsMqWorker struct {
	conn       *amqp.Connection
	channel    *amqp.Channel
	exchange   string
	queue      string
	routingKey string
	sendFn     SendFunc
	ctx        context.Context
	cancel     context.CancelFunc
	wg         sync.WaitGroup
}

var instance *WsMqWorker
var lock sync.RWMutex

func PublishSuccess(uid int64, cmd string, data map[string]interface{}) error {
	return Publish(uid, cmd, 200, "Success", data)
}
func PublishError(uid int64, cmd string, data map[string]interface{}) error {
	return Publish(uid, cmd, 400, "error", data)
}
func Publish(uid int64, cmd string, status int64, message string, data map[string]interface{}) error {
	lock.Lock()
	if instance == nil {
		conf := cfg.LoadRabbitMq()
		amqpUrl := cfg.GetRabbitMqAddr(conf)
		var err error
		instance, err = NewWsProducer(amqpUrl, "topic.ws.exchange", "ws.notice")
		if err != nil {
			log.Errorf("NewWsProducer error: %v", err)
			return err
		}
	}
	lock.Unlock()
	lock.RLock()
	defer lock.RUnlock()
	return instance.Publish("send", uid, map[string]interface{}{
		"cmd":     cmd,
		"status":  status,
		"data":    data,
		"message": message,
	})
}

// 新增：生产者构造函数
func NewWsProducer(amqpURL, exchange string, routingKey string) (*WsMqWorker, error) {
	conn, err := amqp.Dial(amqpURL)
	if err != nil {
		return nil, fmt.Errorf("dial: %w", err)
	}
	ch, err := conn.Channel()
	if err != nil {
		_ = conn.Close()
		return nil, fmt.Errorf("channel: %w", err)
	}
	if err := ch.ExchangeDeclare(
		exchange, "topic", true, false, false, false, nil,
	); err != nil {
		_ = ch.Close()
		_ = conn.Close()
		return nil, fmt.Errorf("exchange.declare: %w", err)
	}
	ctx, cancel := context.WithCancel(context.Background())
	w := &WsMqWorker{
		conn:       conn,
		channel:    ch,
		exchange:   exchange,
		routingKey: routingKey,
		ctx:        ctx,
		cancel:     cancel,
	}
	go w.reconnectProducer(amqpURL, exchange)
	return w, nil
}

// 新增：消费者构造函数
func NewWsConsumer(amqpURL, exchange, queue, routingKey string, sendFn SendFunc) (*WsMqWorker, error) {
	conn, err := amqp.Dial(amqpURL)
	if err != nil {
		return nil, fmt.Errorf("dial: %w", err)
	}
	ch, err := conn.Channel()
	if err != nil {
		_ = conn.Close()
		return nil, fmt.Errorf("channel: %w", err)
	}
	if err := ch.ExchangeDeclare(
		exchange, "topic", true, false, false, false, nil,
	); err != nil {
		_ = ch.Close()
		_ = conn.Close()
		return nil, fmt.Errorf("exchange.declare: %w", err)
	}
	args := amqp.Table{
		"x-max-priority": int32(10),
		"x-expires":      60000,
	}
	q, err := ch.QueueDeclare(queue, true, true, false, false, args)
	if err != nil {
		_ = ch.Close()
		_ = conn.Close()
		return nil, fmt.Errorf("queue.declare: %w", err)
	}
	if err := ch.QueueBind(q.Name, routingKey, exchange, false, nil); err != nil {
		_ = ch.Close()
		_ = conn.Close()
		return nil, fmt.Errorf("queue.bind: %w", err)
	}
	ctx, cancel := context.WithCancel(context.Background())
	w := &WsMqWorker{
		conn:       conn,
		channel:    ch,
		exchange:   exchange,
		queue:      q.Name,
		routingKey: routingKey,
		sendFn:     sendFn,
		ctx:        ctx,
		cancel:     cancel,
	}
	w.wg.Add(1)
	go w.consumeLoop()
	go w.reconnectConsumer(amqpURL, exchange, queue, routingKey, sendFn)
	return w, nil
}

// 断线重连（生产者）
func (w *WsMqWorker) reconnectProducer(amqpURL, exchange string) {
	notify := w.conn.NotifyClose(make(chan *amqp.Error))
	for {
		err, ok := <-notify
		if !ok || err == nil {
			return
		}
		log.Errorf("[MQ Producer] connection closed, try reconnect: %v", err)
		// 关闭旧连接
		if w.channel != nil {
			_ = w.channel.Close()
		}
		if w.conn != nil {
			_ = w.conn.Close()
		}
		for {
			conn, err := amqp.Dial(amqpURL)
			if err != nil {
				log.Errorf("[MQ Producer] reconnect failed: %v", err)
				time.Sleep(5 * time.Second)
				continue
			}
			ch, err := conn.Channel()
			if err != nil {
				_ = conn.Close()
				log.Errorf("[MQ Producer] channel failed: %v", err)
				time.Sleep(5 * time.Second)
				continue
			}
			if err := ch.ExchangeDeclare(exchange, "topic", true, false, false, false, nil); err != nil {
				_ = ch.Close()
				_ = conn.Close()
				log.Errorf("[MQ Producer] exchange declare failed: %v", err)
				time.Sleep(5 * time.Second)
				continue
			}
			w.conn = conn
			w.channel = ch
			log.Infof("[MQ Producer] reconnect success")
			notify = w.conn.NotifyClose(make(chan *amqp.Error))
			lock.Lock()
			instance = w
			lock.Unlock()
			break
		}
	}
}

// 断线重连（消费者）
func (w *WsMqWorker) reconnectConsumer(amqpURL, exchange, queue, routingKey string, sendFn SendFunc) {
	notify := w.conn.NotifyClose(make(chan *amqp.Error))
	for {
		err, ok := <-notify
		if !ok || err == nil {
			return
		}
		log.Errorf("[MQ Consumer] connection closed, try reconnect: %v", err)
		// 关闭旧连接
		if w.channel != nil {
			_ = w.channel.Close()
		}
		if w.conn != nil {
			_ = w.conn.Close()
		}
		for {
			conn, err := amqp.Dial(amqpURL)
			if err != nil {
				log.Errorf("[MQ Consumer] reconnect failed: %v", err)
				time.Sleep(5 * time.Second)
				continue
			}
			ch, err := conn.Channel()
			if err != nil {
				_ = conn.Close()
				log.Errorf("[MQ Consumer] channel failed: %v", err)
				time.Sleep(5 * time.Second)
				continue
			}
			if err := ch.ExchangeDeclare(exchange, "topic", true, false, false, false, nil); err != nil {
				_ = ch.Close()
				_ = conn.Close()
				log.Errorf("[MQ Consumer] exchange declare failed: %v", err)
				time.Sleep(5 * time.Second)
				continue
			}
			args := amqp.Table{
				"x-max-priority": int32(10),
				"x-expires":      60000,
			}
			q, err := ch.QueueDeclare(queue, true, true, false, false, args)
			if err != nil {
				_ = ch.Close()
				_ = conn.Close()
				log.Errorf("[MQ Consumer] queue declare failed: %v", err)
				time.Sleep(5 * time.Second)
				continue
			}
			if err := ch.QueueBind(q.Name, routingKey, exchange, false, nil); err != nil {
				_ = ch.Close()
				_ = conn.Close()
				log.Errorf("[MQ Consumer] queue bind failed: %v", err)
				time.Sleep(5 * time.Second)
				continue
			}
			w.conn = conn
			w.channel = ch
			w.queue = q.Name
			w.routingKey = routingKey
			w.sendFn = sendFn
			w.wg.Add(1)
			go w.consumeLoop()
			log.Infof("[MQ Consumer] reconnect success")
			notify = w.conn.NotifyClose(make(chan *amqp.Error))
			break
		}
	}
}

// 兼容原有接口：GetWsMqWorker/ NewWsMqWorker
// 如果queue为空，只初始化生产者，否则初始化消费者
func GetWsMqWorker(amqpURL, exchange, queue, routingKey string, sendFn SendFunc) (*WsMqWorker, error) {
	if queue == "" {
		return NewWsProducer(amqpURL, exchange, routingKey)
	}
	return NewWsConsumer(amqpURL, exchange, queue, routingKey, sendFn)
}

func NewWsMqWorker(amqpURL, exchange, queue, routingKey string, sendFn SendFunc) (*WsMqWorker, error) {
	return GetWsMqWorker(amqpURL, exchange, queue, routingKey, sendFn)
}

// consumeLoop continuously consumes and dispatches messages until ctx is cancelled.
func (w *WsMqWorker) consumeLoop() {
	defer w.wg.Done()
	msgs, err := w.channel.Consume(
		w.queue, "", false, false, false, false, nil,
	)
	if err != nil {
		log.Errorf("consume error: %v", err)
		return
	}

	for {
		select {
		case d, ok := <-msgs:
			if !ok {
				return // channel closed
			}
			w.handleDelivery(d)
		case <-w.ctx.Done():
			return
		}
	}
}

// handleDelivery processes one AMQP delivery, acks or nacks accordingly.
func (w *WsMqWorker) handleDelivery(d amqp.Delivery) {
	// expect [[class, method], args]
	var raw [][]interface{}
	log.Debugf("[WebSocket]: 收到消息: %v", string(d.Body))
	if err := json.Unmarshal(d.Body, &raw); err != nil {
		log.Errorf("json unmarshal err: %v", err)
		d.Nack(false, false)
		return
	}

	// extract args: [ [class,method], args... ]
	if len(raw) != 2 {
		log.Errorf("invalid format: %+v", raw)
		d.Nack(false, false)
		return
	}
	meta := raw[0]
	if len(meta) != 2 {
		log.Errorf("invalid meta: %+v", raw[0])
		d.Nack(false, false)
		return
	}
	method, _ := meta[1].(string)
	args := raw[1]

	// only support "send" for now
	switch method {
	case "send":
		// uid arg
		var uid int64
		switch args[0].(type) {
		case float64:
			f0 := args[0].(float64)
			uid = int64(f0)
		case int64:
			uid = args[0].(int64)
		case int:
			uid = int64(args[0].(int))
		case string:
			f0, err := strconv.ParseInt(args[0].(string), 10, 64)
			if err != nil {
				log.Errorf("send uid type invalid: %+v", args[0])
				d.Nack(false, false)
				return
			}
			uid = f0
		default:
			log.Errorf("send uid type invalid: %+v", args[0])
			d.Nack(false, false)
			return

		}
		// data arg
		data, ok := args[1].(map[string]interface{})
		if !ok {
			log.Errorf("send data type invalid: %+v", args[1])
			d.Nack(false, false)
			return
		}
		if err := w.sendFn(uid, data); err != nil {
			// log.Errorf("sendFn error: %v", err)
			d.Nack(false, false)
			return
		}
	default:
		log.Errorf("unknown method: %s", method)
		d.Nack(false, false)
		return
	}

	d.Ack(false)
}

// Publish sends a JSON‑encoded command to RabbitMQ, blocking until confirmed.
func (w *WsMqWorker) Publish(method string, args ...interface{}) error {
	// prepare payload [[class, method], args]
	payload := [][]interface{}{
		{`App\Sockets\Process\WsMqJob`, method},
		args,
	}
	body, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("json marshal: %w", err)
	}

	// // enable publisher confirms
	// if err := w.channel.Confirm(false); err != nil {
	// 	return fmt.Errorf("channel confirm: %w", err)
	// }

	// confirms := w.channel.NotifyPublish(make(chan amqp.Confirmation, 1))

	if err := w.channel.PublishWithContext(
		context.Background(),
		w.exchange, w.routingKey, false, false,
		amqp.Publishing{
			Body:         body,
			DeliveryMode: amqp.Transient,
			Priority:     10,
		},
	); err != nil {
		return fmt.Errorf("publish: %w", err)
	}

	// wait for confirmation or timeout
	// select {
	// case conf := <-confirms:
	// 	if !conf.Ack {
	// 		return errors.New("publish not acked")
	// 	}
	// case <-time.After(5 * time.Second):
	// 	return errors.New("publish ack timeout")
	// }
	log.Infof("published method=%s args=%+v", method, args)
	return nil
}

// Close shuts down the consumer and closes the AMQP channel/connection.
func (w *WsMqWorker) Close() error {
	w.cancel()
	w.wg.Wait()
	if err := w.channel.Close(); err != nil {
		return err
	}
	return w.conn.Close()
}
