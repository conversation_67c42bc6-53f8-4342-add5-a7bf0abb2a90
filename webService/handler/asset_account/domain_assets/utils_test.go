package domain_assets

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"micro-service/pkg/cfg"
	dac "micro-service/middleware/mysql/domain_assets_cron"
)

func init() {
	// 初始化配置以避免测试中的 nil pointer 错误
	cfg.InitLoadCfg()
}

func TestAddCronLocalClient(t *testing.T) {
	// 模拟本地化环境
	originalPlatform := cfg.LoadCommon().Client.Platform
	defer func() {
		// 恢复原始配置
		cfg.GetInstance().Common.Client.Platform = originalPlatform
	}()
	
	// 设置为本地化环境
	cfg.GetInstance().Common.Client.Platform = "local"
	
	ctx := context.Background()
	userId := uint64(123)
	opId := int64(456)
	spec := "0 0 1 * * *"
	
	cronId, err := addCron(ctx, userId, opId, spec)
	
	assert.NoError(t, err, "本地化环境下 addCron 应该成功")
	assert.Equal(t, userId*1000, cronId, "本地化环境下应该返回模拟的 cron ID")
}

func TestUpdateCronLocalClient(t *testing.T) {
	// 模拟本地化环境
	originalPlatform := cfg.LoadCommon().Client.Platform
	defer func() {
		// 恢复原始配置
		cfg.GetInstance().Common.Client.Platform = originalPlatform
	}()
	
	// 设置为本地化环境
	cfg.GetInstance().Common.Client.Platform = "local"
	
	ctx := context.Background()
	info := &dac.Cron{
		UserId:         123,
		OperateUserId:  456,
		CronId:         789,
		Status:         dac.Enable,
		CronExpression: "0 0 1 * * *",
	}
	
	err := updateCron(ctx, info)
	
	assert.NoError(t, err, "本地化环境下 updateCron 应该成功")
}

func TestAddCronSaasClient(t *testing.T) {
	// 模拟SaaS环境
	originalPlatform := cfg.LoadCommon().Client.Platform
	defer func() {
		// 恢复原始配置
		cfg.GetInstance().Common.Client.Platform = originalPlatform
	}()
	
	// 设置为SaaS环境
	cfg.GetInstance().Common.Client.Platform = "saas"
	
	ctx := context.Background()
	userId := uint64(123)
	opId := int64(456)
	spec := "0 0 1 * * *"
	
	// 在测试环境中，这个调用可能会失败，因为没有真实的 cron 服务
	// 但我们主要是测试代码路径是否正确
	_, err := addCron(ctx, userId, opId, spec)
	
	// 在测试环境中，我们期望这个调用会尝试连接 cron 服务
	// 如果失败是因为服务不可用，这是正常的
	t.Logf("SaaS环境下 addCron 调用结果: %v", err)
}
