package cron

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGetProtoClient(t *testing.T) {
	// 测试客户端创建不会panic
	client := GetProtoClient()
	assert.NotNil(t, client, "Proto client should not be nil")
}

func TestGetMicroClient(t *testing.T) {
	// 测试微服务客户端创建不会panic
	client := GetMicroClient()
	assert.NotNil(t, client, "Micro client should not be nil")
}

func TestGetInstance(t *testing.T) {
	// 测试单例模式
	client1 := getInstance()
	client2 := getInstance()
	
	assert.NotNil(t, client1, "First instance should not be nil")
	assert.NotNil(t, client2, "Second instance should not be nil")
	assert.Equal(t, client1, client2, "Should return same instance (singleton)")
}
